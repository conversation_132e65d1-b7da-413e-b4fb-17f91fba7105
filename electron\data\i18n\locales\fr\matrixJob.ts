/**
 * Tâche de matrice liée - Français
 */
export default {
  // Journaux de tâches
  logs: {
    taskJobParams: "Paramètres TaskJob",
    pauseTaskJob: "Suspendre TaskJob, ID de tâche",
    resumeTaskJob: "Reprendre TaskJob, ID de tâche, ID de processus",
    doTaskStart: "Commencer l'exécution de la tâche",
    allFinished: "Toutes les tâches terminées"
  },

  // Étapes de tâche
  steps: {
    connect: "Connecter",
    download: "Télécharger", 
    import: "Importer"
  },

  // Messages de tâche
  messages: {
    connectDevice: "Connecter l'appareil",
    executeFileDownload: "Exécuter le téléchargement de fichier",
    downloadingFile: "Téléchargement de fichier en cours",
    downloadFileFailed: "Échec du téléchargement de fichier",
    fileDownloadCompleted: "Téléchargement de fichier terminé",
    executeParamImport: "Exécuter l'importation de paramètres",
    paramValidationFailed: "Échec de la validation du format des paramètres",
    paramImportFailed: "Échec de l'importation des paramètres",
    paramImportCompleted: "Importation des paramètres terminée",
    taskCompleted: "Tâche terminée",
    deviceConnectionFailed: "Échec de la connexion de l'appareil",
    deviceRebootSuccess: "Redémarrage de l'appareil réussi"
  },

  // Messages d'erreur
  errors: {
    paramItemModifyError: "Erreur de modification d'élément de paramètre, les éléments d'erreur sont : ",
    paramConfirmError: "Erreur de confirmation de paramètre, raison : ",
    paramNotFound: "Raison de l'erreur : paramètre correspondant non trouvé",
    invalidValue: "valeur invalide",
    description: "description",
    errorReason: ", raison de l'erreur : "
  }
};
