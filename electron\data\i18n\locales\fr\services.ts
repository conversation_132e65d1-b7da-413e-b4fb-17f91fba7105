/**
 * Relatif aux services - Français
 */
export default {
  // Service de licence
  license: {
    cacheResult: "Utilisation du résultat en cache",
    verificationTime: "Temps de vérification",
    verificationFailed: "Vérification échouée, temps",
    businessErrorInfo: "Obtenir les informations d'erreur métier",
    getMachineCode: "Obtenir le code machine",
    checkAuth: "Vérifier l'autorisation",
    activate: "Activer la licence",
  },

  // Service multiplateforme
  cross: {
    startGoService: "Démarrage du service Go...",
    goServiceStartSuccess: "Service Go démarré avec succès, temps",
    goServiceStartFailed: "Démarrage du service Go échoué, temps",
    startPythonService: "Démarrage du service Python...",
    pythonServiceStartSuccess: "Service Python démarré avec succès, temps",
    pythonServiceStartFailed: "Démarrage du service Python échoué, temps",
    optimizeStartParams: "Optimiser les paramètres de démarrage",
  },

  // Service de base
  base: {
    getClientStart: "Commencer à obtenir le client d'appareil, ID d'appareil",
    deviceNotFound: "Informations d'appareil non trouvées, ID d'appareil",
    deviceNotConnected:
      "Appareil non connecté ou client invalide, ID d'appareil",
    getClientSuccess: "Client d'appareil obtenu avec succès, ID d'appareil",
  },

  // Service de connexion d'appareil
  deviceConnect: {
    deviceConnected: "Appareil connecté",
    connectionAttempt: "Tentative de connexion",
    clientObtained: "Client obtenu, ID d'appareil",
    clientNotObtained: "Client non obtenu, ID d'appareil",
    connectionException: "Exception de connexion",
    connectionCheckException: "Exception de vérification de connexion",
    connectDeviceByRpc: "Connecter l'appareil par RPC",
    callInterface: "Appeler l'interface",
    useDeviceIdAsKey: "Utiliser l'ID d'appareil comme clé",
    getConnectionFromGlobal:
      "Obtenir l'objet de connexion des variables globales",
    notifyFrontendConnectionStatus:
      "Notifier le frontend pour modifier l'état de connexion",
    retryInterval: "Optionnel : intervalle entre les tentatives",
    cacheSuccessfulConnection: "Mettre en cache l'objet de connexion réussie",
    resetData: "Réinitialiser les données",
    compareMd5AndSetFilePath: "Comparer MD5 et définir le chemin du fichier",
    addDebugInfoToGlobal:
      "Ajouter debugInfo et debugItemMap à realSingleGlobalDeviceInfo",
    connectionSuccessful: "Connexion réussie, ID d'appareil",
    disconnectDevice: "Déconnecter l'appareil",
    getConnection: "Obtenir la connexion",
    deleteConnectionObject: "Supprimer l'objet de connexion",
    disconnectSuccessful: "Déconnexion réussie, ID d'appareil",
    disconnectException: "Exception de déconnexion",
  },

  // Service d'informations d'appareil
  deviceInfo: {
    getDeviceInfoStart: "Commencer à obtenir les informations d'appareil",
    exportStart: "Commencer l'exportation des informations d'appareil",
    exportSuccess: "Exportation des informations d'appareil réussie",
  },

  // Service de configuration
  configure: {
    getConfigureList: "Obtenir la liste de configuration",
    addConfigure: "Ajouter configuration",
    setId: "Définir ID",
    projectNotExists: "Le projet n'existe pas",
    duplicateName: "Nom en double, veuillez ressaisir",
    addConfigureException: "Exception d'ajout de configuration",
    projectNotFound: "Projet non trouvé",
    projectPathNotFound: "Chemin de projet non trouvé",
    replaceWithNew: "Remplacer par nouveau contenu",
    projectNotFoundShort: "Projet non trouvé",
    operationTypeIncorrect:
      "Type d'opération incorrect, valeurs autorisées sont [project,hmi]",
    renameConfigureException: "Exception de renommage de configuration",
    getConfigureListException:
      "Exception d'obtention de liste de configuration",
    configureSaveException: "Exception de sauvegarde de configuration",
    openConfigureFolder: "Ouvrir dossier de configuration",
  },

  // Service de fenêtre
  window: {
    windowStateInitialized: "Gestion d'état de fenêtre initialisée",
    windowStateInitFailed:
      "Échec d'initialisation de gestion d'état de fenêtre",
    windowStateSaved: "État de fenêtre sauvegardé",
    saveWindowStateFailed: "Échec de sauvegarde d'état de fenêtre",
    windowStateRestored: "État de fenêtre restauré",
    restoreWindowStateFailed: "Échec de restauration d'état de fenêtre",
    clickNotification: "Clic sur notification",
    closeNotification: "Fermer notification",
    createWindow: "Créer fenêtre",
    windowCreated: "Fenêtre créée",
    windowCreationFailed: "Échec de création de fenêtre",
    getWindowId: "Obtenir ID de fenêtre",
    windowCommunication: "Communication entre fenêtres",
    notificationCreated: "Notification créée",
    windowClosed: "Fenêtre fermée",
    windowMaximized: "Fenêtre maximisée",
    windowMinimized: "Fenêtre minimisée",
    windowDragged: "Glissement de fenêtre terminé",
    screenshotTaken: "Capture d'écran sauvegardée",
    devToolsOpened: "Outils de développement ouverts",
    devToolsClosed: "Outils de développement fermés",
  },

  // Service d'événements système
  systemEvents: {
    systemEventsInitialized: "Surveillance d'événements système initialisée",
    eventListenersSet: "Écouteurs d'événements système configurés",
    setupEventListenersFailed:
      "Échec de configuration des écouteurs d'événements",
    systemSuspending: "Système en suspension",
    systemResuming: "Système en reprise",
    screenLocked: "Écran verrouillé",
    screenUnlocked: "Écran déverrouillé",
    systemShuttingDown: "Système en arrêt",
    appBeforeQuit: "Application avant fermeture",
    allWindowsClosed: "Toutes les fenêtres fermées",
    appActivated: "Application activée",
    windowBlurred: "Fenêtre sans focus",
    windowFocused: "Fenêtre avec focus",
    windowMinimized: "Fenêtre minimisée",
    windowRestored: "Fenêtre restaurée",
    applicationStateSaved: "État d'application sauvegardé",
    applicationStateRestored: "État d'application restauré",
    powerInfoRetrieved: "Informations d'alimentation obtenues",
    cleanupCompleted: "Nettoyage terminé",
  },

  // Service de barre des tâches
  tray: {
    trayLoaded: "Barre des tâches chargée",
    trayCreated: "Barre des tâches créée",
    trayCreationFailed: "Échec de création de barre des tâches",
  },

  // Service de base de données
  database: {
    databaseConnected: "Base de données connectée",
    databaseConnectionFailed: "Échec de connexion à la base de données",
    queryExecuted: "Requête exécutée",
    queryFailed: "Requête échouée",
    dataInserted: "Données insérées",
    dataUpdated: "Données mises à jour",
    dataDeleted: "Données supprimées",
    transactionStarted: "Transaction démarrée",
    transactionCommitted: "Transaction validée",
    transactionRolledBack: "Transaction annulée",
    tableCreated: "Table créée",
    tableCreationFailed: "Échec de création de table",
    dataInsertFailed: "Échec d'insertion de données",
    dataDeleteFailed: "Échec de suppression de données",
    dataUpdateFailed: "Échec de mise à jour de données",
    dataDirRetrieved: "Répertoire de données récupéré",
    dataDirRetrieveFailed: "Échec de récupération du répertoire de données",
    customDataDirSet: "Répertoire de données personnalisé défini",
    customDataDirSetFailed:
      "Échec de définition du répertoire de données personnalisé",
  },

  // Service HMI
  hmi: {
    graphDefined: "Graphique défini",
    configurationLoaded: "Configuration chargée",
    viewCreated: "Vue créée",
    projectOpened: "Projet ouvert",
    projectSaved: "Projet sauvegardé",
    elementAdded: "Élément ajouté",
    elementModified: "Élément modifié",
    elementDeleted: "Élément supprimé",
  },

  // Service de tâche
  job: {
    jobStarted: "Tâche démarrée",
    jobCompleted: "Tâche terminée",
    jobFailed: "Tâche échouée",
    jobCancelled: "Tâche annulée",
    jobPaused: "Tâche en pause",
    jobResumed: "Tâche reprise",
    jobScheduled: "Tâche planifiée",
    jobRemoved: "Tâche supprimée",
  },

  // Service de matrice
  matrix: {
    matrixInitialized: "Matrice initialisée",
    matrixCalculationStarted: "Calcul de matrice démarré",
    matrixCalculationCompleted: "Calcul de matrice terminé",
    matrixOperationFailed: "Opération de matrice échouée",
    dataProcessed: "Données traitées",
    algorithmExecuted: "Algorithme exécuté",
  },
};
