/**
 * Trabajo de tarea de matriz relacionado - Español
 */
export default {
  // Registros de tareas
  logs: {
    taskJobParams: "Parámetros TaskJob",
    pauseTaskJob: "Pausar TaskJob, ID de tarea",
    resumeTaskJob: "<PERSON><PERSON><PERSON>ob, ID de tarea, ID de proceso",
    doTaskStart: "Comenzar ejecución de tarea",
    allFinished: "Todas las tareas completadas"
  },

  // Pasos de tarea
  steps: {
    connect: "Conectar",
    download: "Descargar", 
    import: "Importar"
  },

  // Mensajes de tarea
  messages: {
    connectDevice: "Conectar dispositivo",
    executeFileDownload: "Ejecutar descarga de archivo",
    downloadingFile: "Descargando archivo",
    downloadFileFailed: "Fallo en descarga de archivo",
    fileDownloadCompleted: "Descarga de archivo completada",
    executeParamImport: "Ejecutar importación de parámetros",
    paramValidationFailed: "Fallo en validación de formato de parámetros",
    paramImportFailed: "Fallo en importación de parámetros",
    paramImportCompleted: "Importación de parámetros completada",
    taskCompleted: "Tarea completada",
    deviceConnectionFailed: "Fallo en conexión de dispositivo",
    deviceRebootSuccess: "Reinicio de dispositivo exitoso"
  },

  // Mensajes de error
  errors: {
    paramItemModifyError: "Error de modificación de elemento de parámetro, los elementos de error son: ",
    paramConfirmError: "Error de confirmación de parámetro, razón: ",
    paramNotFound: "Razón del error: parámetro correspondiente no encontrado",
    invalidValue: "valor inválido",
    description: "descripción",
    errorReason: ", razón del error: "
  }
};
