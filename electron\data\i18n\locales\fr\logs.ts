/**
 * Messages de journal - Français
 */
export default {
  reportController: {
    getCommonReportListEntry: "getCommonReportList paramètres d'entrée",
    getCommonReportListReturn: "getCommonReportList retour",
    getCommonReportListError: "getCommonReportList exception",
    cancelUploadStart: "Annuler le téléchargement du fichier d'onde commencé",
    cancelUploadError: "Annuler le téléchargement du fichier d'onde exception",
    openWaveFileStart: "Ouvrir le fichier d'onde commencé",
    openWaveFileError: "Ouvrir le fichier d'onde exception",
    getGroupReportStart: "Obtenir le rapport de groupe commencé",
    getGroupReportError: "Obtenir le rapport de groupe exception",
    getOperateReportStart: "Obtenir le rapport d'opération commencé",
    getOperateReportError: "Obtenir le rapport d'opération exception",
    getAuditReportStart: "Obtenir le rapport d'audit commencé",
    getAuditReportError: "Obtenir le rapport d'audit exception",
    exportCommonReportStart: "Exporter le rapport commun commencé",
    exportCommonReportError: "Exporter le rapport commun exception",
    clearReportStart: "Effacer le rapport commencé",
    clearReportError: "Effacer le rapport exception",
    refreshReportStart: "Actualiser le rapport commun commencé",
    refreshReportError: "Actualiser le rapport commun exception",
    refreshGroupReportStart: "Actualiser le rapport de groupe commencé",
    refreshGroupReportError: "Actualiser le rapport de groupe exception",
    refreshOperateReportStart: "Actualiser le rapport d'opération commencé",
    refreshOperateReportError: "Actualiser le rapport d'opération exception",
    refreshTripReportStart: "Actualiser le rapport de déclenchement commencé",
    refreshTripReportError: "Actualiser le rapport de déclenchement exception",
    uploadWaveStart: "Téléchargement du fichier d'onde commencé",
    uploadWaveError: "Téléchargement du fichier d'onde exception",
  },
  configureService: {
    getConfigureListError:
      "Erreur lors de l'obtention de la liste de configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
  },
  // Journaux du contrôleur
  configureController: {
    initialized: "Contrôleur initialisé",
    getConfigureListStart: "Commencer à obtenir la liste de configuration",
    getConfigureListError:
      "Erreur lors de l'obtention de la liste de configuration",
    addConfigureStart: "Commencer à ajouter la configuration",
    addConfigureError: "Erreur lors de l'ajout de la configuration",
    renameConfigureStart: "Commencer à renommer la configuration",
    renameConfigureError: "Erreur lors du renommage de la configuration",
    removeConfigureStart: "Commencer à supprimer la configuration",
    removeConfigureError: "Erreur lors de la suppression de la configuration",
    saveConfigureStart: "Commencer à sauvegarder la configuration",
    saveConfigureError: "Erreur lors de la sauvegarde de la configuration",
    loadConfigureStart: "Commencer à charger la configuration",
    loadConfigureError: "Erreur lors du chargement de la configuration",
    openConfigureDirStart: "Commencer à ouvrir le répertoire de configuration",
    openConfigureDirError:
      "Erreur lors de l'ouverture du répertoire de configuration",
  },
  deviceConnectController: {
    initialized: "Contrôleur initialisé",
    connectDeviceStart:
      "Commencer la connexion du dispositif, paramètres de connexion",
    connectDeviceCallService:
      "Appeler la couche de service pour connecter le dispositif",
    connectDeviceServiceResult: "Résultat de retour de la couche de service",
    connectDeviceSuccess: "Connexion du dispositif réussie",
    connectDeviceGetError:
      "Obtenir les informations d'erreur, sortie de journal",
    connectDeviceFailed:
      "Connexion du dispositif échouée, informations d'erreur",
    connectDeviceException: "Capturer l'exception, sortie de journal",
    connectDeviceExceptionDetail: "Exception de connexion du dispositif",
    disconnectDeviceStart:
      "Commencer la déconnexion du dispositif, ID du dispositif",
    disconnectDeviceCheckStatus: "Vérifier l'état de connexion du dispositif",
    disconnectDeviceAlready: "Dispositif déjà déconnecté, ID du dispositif",
    disconnectDeviceCallService:
      "Appeler la couche de service pour déconnecter le dispositif",
    disconnectDeviceResult: "Résultat de déconnexion",
    disconnectDeviceSuccess:
      "Déconnexion du dispositif réussie, ID du dispositif",
    disconnectDeviceException:
      "Exception de déconnexion du dispositif, ID du dispositif",
    disconnectDeviceFailed:
      "Déconnexion du dispositif échouée, ID du dispositif",
  },
  deviceOperateController: {
    initialized: "Contrôleur initialisé",
    addDeviceStart:
      "Commencer à ajouter la configuration du dispositif, paramètres de demande",
    addDeviceCallService:
      "Appeler la couche de service pour ajouter la configuration du dispositif",
    addDeviceSuccess: "Ajouter la configuration du dispositif réussi, résultat",
    addDeviceException:
      "Exception lors de l'ajout de la configuration du dispositif",
    updateDeviceStart:
      "Commencer à mettre à jour la configuration du dispositif, paramètres de demande",
    updateDeviceCallService:
      "Appeler la couche de service pour mettre à jour la configuration du dispositif",
    updateDeviceSuccess:
      "Mettre à jour la configuration du dispositif réussi, résultat",
    updateDeviceException:
      "Exception lors de la mise à jour de la configuration du dispositif",
    removeDeviceStart:
      "Commencer à supprimer la configuration du dispositif, paramètres de demande",
    removeDeviceCallService:
      "Appeler la couche de service pour supprimer la configuration du dispositif, ID du dispositif",
    removeDeviceResult:
      "Résultat de suppression de la configuration du dispositif",
    removeDeviceSuccess:
      "Supprimer la configuration du dispositif réussi, ID du dispositif",
    removeDeviceFailed:
      "Supprimer la configuration du dispositif échoué, ID du dispositif",
    removeDeviceException:
      "Exception lors de la suppression de la configuration du dispositif, ID du dispositif",
    getDeviceListStart:
      "Commencer à obtenir la liste de configuration du dispositif",
    getDeviceListCallService:
      "Appeler la couche de service pour obtenir la liste de configuration du dispositif",
    getDeviceListSuccess:
      "Obtenir la liste de configuration du dispositif réussi, nombre de dispositifs",
    getDeviceListException:
      "Exception lors de l'obtention de la liste de configuration du dispositif",
  },
  paramService: {
    getDiffParamComplete: "Comparaison terminée, groupes de différence",
    getAllDiffParamError: "getAllDiffParam erreur",
    getParamInfoEntry: "getParamInfo paramètres d'entrée",
    getParamInfoReturn: "getParamInfo retour",
    getParamInfoError: "getParamInfo exception",
    startGetParamInfo: "Commencer à obtenir les paramètres, pagination",
    getAllParamInfoStart: "Commencer à obtenir tous les paramètres",
    getAllParamInfoSuccess: "Obtenu avec succès tous les paramètres, total",
    modifyParamStart: "Commencer à modifier les paramètres",
    validateParam: "Valider l'élément de paramètre",
    validateFailed: "Validation du paramètre échouée",
    validatePassed: "Validation du paramètre réussie, prêt à envoyer",
    setTimeout: "Définir le délai d'attente",
    sendResponse: "Envoyer la réponse",
    modifySuccess: "Modification réussie",
    sendFailed: "Envoi échoué",
    businessError: "Erreur métier",
    getAllDiffParamStart: "Commencer la comparaison de paramètres par lots",
    excelParseFailed: "Analyse Excel échouée",
    csvParseFailed: "Analyse CSV échouée",
    xmlParseFailed: "Analyse XML échouée",
    fileParseComplete: "Analyse de fichier terminée",
    getDiffParamStart:
      "Commencer la comparaison de paramètres de groupe unique",
    diffComplete: "Comparaison terminée, éléments de différence",
    importParamStart: "Commencer l'importation des paramètres",
    paramReady: "Paramètres prêts à envoyer",
    importSuccess: "Importation réussie",
    exportAllParamStart: "Commencer l'exportation de tous les paramètres",
    exportComplete: "Exportation terminée",
    exportParamStart: "Commencer l'exportation des paramètres de groupe",
    getGroupItemsStart: "Obtenir les éléments de paramètres de groupe",
    getParamValueFailed: "Échec de l'obtention de la valeur du paramètre",
    getGroupItemsComplete: "Obtention terminée, éléments de paramètres",
    getAllGroupItemsStart: "Obtenir tous les éléments de paramètres de groupe",
    groupParamCount: "Groupe : {group}, éléments de paramètres : {count}",
    getCurrentRunAreaStart: "Obtenir la zone d'exécution actuelle",
    getCurrentRunAreaSuccess: "Obtention réussie",
    getCurrentRunAreaFailed: "Obtention échouée",
    selectRunAreaStart: "Sélectionner la zone de paramétrage",
    runAreaEmpty: "La zone de paramétrage ne peut pas être vide",
    selectRunAreaSuccess: "Sélection réussie",
    selectRunAreaFailed: "Sélection échouée",
  },
  debugInfoMenuService: {
    initialized: "Initialisation terminée",
    getDebugInfoEntry: "getDebugInfo paramètres d'entrée",
    getTreeMenuError: "getTreeMenu exception",
    getTreeMenuComplete: "Traitement terminé, nombre de menus",
  },
  deviceInfoController: {
    initialized: "Contrôleur initialisé",
    getDeviceInfoStart:
      "Commencer à obtenir les informations du dispositif, paramètres de demande",
    getDeviceInfoCheckConnection: "Vérifier l'état de connexion du dispositif",
    getDeviceInfoNotConnected:
      "Dispositif non connecté, impossible d'obtenir les informations du dispositif",
    getDeviceInfoConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les informations du dispositif",
    getDeviceInfoSuccess:
      "Obtenir les informations du dispositif réussi, nombre de résultats",
    getDeviceInfoException:
      "Exception lors de l'obtention des informations du dispositif",
    exportDeviceInfoStart:
      "Commencer à exporter les informations du dispositif, paramètres de demande",
    exportDeviceInfoCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    exportDeviceInfoNotConnected:
      "Dispositif non connecté, impossible d'exporter les informations du dispositif",
    exportDeviceInfoValidateParams:
      "Valider les paramètres d'exportation, nombre de données",
    exportDeviceInfoPath: "Chemin d'exportation",
    exportDeviceInfoEmptyData: "Les données d'exportation sont vides",
    exportDeviceInfoEmptyPath: "Le chemin du fichier d'exportation est vide",
    exportDeviceInfoFileExtension: "Extension de fichier",
    exportDeviceInfoUnsupportedFormat: "Format de fichier non pris en charge",
    exportDeviceInfoDirPath: "Chemin du répertoire d'exportation",
    exportDeviceInfoCreateDir: "Créer le répertoire d'exportation",
    exportDeviceInfoCreateDirFailed:
      "Échec de la création du répertoire d'exportation",
    exportDeviceInfoCallService:
      "Appeler la couche de service pour exporter les informations du dispositif",
    exportDeviceInfoSuccess:
      "Exportation des informations du dispositif réussie, chemin d'exportation",
    exportDeviceInfoException:
      "Exception lors de l'exportation des informations du dispositif",
  },
  variableController: {
    getVariableEntry: "getVariable paramètres d'entrée",
    getVariableReturn: "Journal de retour de la méthode obtenir variable",
    getVariableException: "Journal d'exception de la méthode obtenir variable",
    addVariableEntry: "addVariable paramètres d'entrée",
    addVariableReturn: "Journal de retour de la méthode ajouter variable",
    addVariableException: "Journal d'exception de la méthode ajouter variable",
    modifyVariableEntry: "modifyVariable paramètres d'entrée",
    modifyVariableReturn: "Journal de retour de la méthode modifier variable",
    modifyVariableException:
      "Journal d'exception de la méthode modifier variable",
    deleteVariableEntry: "deleteVariable paramètres d'entrée",
    deleteVariableReturn: "Journal de retour de la méthode supprimer variable",
    deleteVariableException:
      "Journal d'exception de la méthode supprimer variable",
    exportVariableEntry: "exportVariable paramètres d'entrée",
    exportVariableEmptyPath: "Le chemin d'exportation ne peut pas être vide",
    exportVariableReturn: "Journal de retour de la méthode exporter variable",
    exportVariableException:
      "Journal d'exception de la méthode exporter variable",
    importVariableEntry: "importVariable paramètres d'entrée",
    importVariableEmptyPath: "Le chemin d'importation ne peut pas être vide",
    importVariableReturn: "Journal de retour de la méthode importer variable",
    importVariableException:
      "Journal d'exception de la méthode importer variable",
  },
  paramController: {
    initialized: "Contrôleur initialisé",
    getParamStart:
      "Commencer à obtenir les paramètres du dispositif, paramètres de demande",
    getParamNotConnected:
      "Dispositif non connecté, impossible d'obtenir les paramètres du dispositif",
    getParamConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les paramètres du dispositif",
    getParamSuccess:
      "Obtenir les paramètres du dispositif réussi, nombre de résultats",
    getParamException:
      "Exception lors de l'obtention des paramètres du dispositif",
    getAllParamStart:
      "Commencer à obtenir tous les paramètres du dispositif, paramètres de demande",
    getAllParamNotConnected:
      "Dispositif non connecté, impossible d'obtenir tous les paramètres du dispositif",
    getAllParamConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir tous les paramètres du dispositif",
    getAllParamSuccess:
      "Obtenir tous les paramètres du dispositif réussi, nombre de résultats",
    getAllParamException:
      "Exception lors de l'obtention de tous les paramètres du dispositif",
    confirmParamStart:
      "Commencer à modifier les paramètres du dispositif, paramètres de demande",
    confirmParamNotConnected:
      "Dispositif non connecté, impossible de modifier les paramètres du dispositif",
    confirmParamConnected:
      "Dispositif connecté, appeler la couche de service pour modifier les paramètres du dispositif",
    confirmParamSuccess:
      "Modifier les paramètres du dispositif réussi, résultat",
    confirmParamException:
      "Exception lors de la modification des paramètres du dispositif",
    getDiffParamStart:
      "Commencer à obtenir les différences de paramètres, paramètres de demande",
    getDiffParamNotConnected:
      "Dispositif non connecté, impossible d'obtenir les différences de paramètres",
    getDiffParamPath: "Chemin d'importation",
    getDiffParamEmptyPath: "Le chemin d'importation est vide",
    getDiffParamConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les différences de paramètres",
    getDiffParamSuccess:
      "Obtenir les différences de paramètres réussi, résultat",
    getDiffParamException:
      "Exception lors de l'obtention des différences de paramètres",
    getAllDiffParamStart:
      "Commencer à obtenir toutes les différences de paramètres, paramètres de demande",
    getAllDiffParamNotConnected:
      "Dispositif non connecté, impossible d'obtenir toutes les différences de paramètres",
    getAllDiffParamPath: "Chemin d'importation",
    getAllDiffParamEmptyPath: "Le chemin d'importation est vide",
    getAllDiffParamConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir toutes les différences de paramètres",
    getAllDiffParamSuccess:
      "Obtenir toutes les différences de paramètres réussi, résultat",
    getAllDiffParamException:
      "Exception lors de l'obtention de toutes les différences de paramètres",
    importParamStart:
      "Commencer à importer les paramètres du dispositif, paramètres de demande",
    importParamNotConnected:
      "Dispositif non connecté, impossible d'importer les paramètres du dispositif",
    importParamConnected:
      "Dispositif connecté, appeler la couche de service pour importer les paramètres du dispositif",
    importParamSuccess:
      "Importer les paramètres du dispositif réussi, résultat",
    importParamException:
      "Exception lors de l'importation des paramètres du dispositif",
    exportParamStart:
      "Commencer à exporter les paramètres du dispositif, paramètres de demande",
    exportParamNotConnected:
      "Dispositif non connecté, impossible d'exporter les paramètres du dispositif",
    exportParamPath: "Chemin d'exportation",
    exportParamEmptyPath: "Le chemin d'exportation est vide",
    exportParamConnected:
      "Dispositif connecté, appeler la couche de service pour exporter les paramètres du dispositif",
    exportParamSuccess:
      "Exporter les paramètres du dispositif réussi, résultat",
    exportParamException:
      "Exception lors de l'exportation des paramètres du dispositif",
    exportAllParamStart:
      "Commencer à exporter tous les paramètres du dispositif, paramètres de demande",
    exportAllParamNotConnected:
      "Dispositif non connecté, impossible d'exporter tous les paramètres du dispositif",
    exportAllParamPath: "Chemin d'exportation",
    exportAllParamEmptyPath: "Le chemin d'exportation est vide",
    exportAllParamConnected:
      "Dispositif connecté, appeler la couche de service pour exporter tous les paramètres du dispositif",
    exportAllParamSuccess:
      "Exporter tous les paramètres du dispositif réussi, résultat",
    exportAllParamException:
      "Exception lors de l'exportation de tous les paramètres du dispositif",
    getCurrentRunAreaStart:
      "Commencer à obtenir la zone d'exécution actuelle, paramètres de demande",
    getCurrentRunAreaNotConnected:
      "Dispositif non connecté, impossible d'obtenir la zone d'exécution actuelle",
    getCurrentRunAreaConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir la zone d'exécution actuelle",
    getCurrentRunAreaSuccess:
      "Obtenir la zone d'exécution actuelle réussi, résultat",
    getCurrentRunAreaException:
      "Exception lors de l'obtention de la zone d'exécution actuelle",
    selectRunAreaStart:
      "Commencer à sélectionner la zone de paramètres, paramètres de demande",
    selectRunAreaNotConnected:
      "Dispositif non connecté, impossible de sélectionner la zone de paramètres",
    selectRunAreaConnected:
      "Dispositif connecté, appeler la couche de service pour sélectionner la zone de paramètres",
    selectRunAreaSuccess: "Sélectionner la zone de paramètres réussi, résultat",
    selectRunAreaException:
      "Exception lors de la sélection de la zone de paramètres",
  },
  remoteControlController: {
    ykSelectEntry:
      "Journal d'entrée de la méthode de sélection de contrôle à distance",
    ykSelectReturn:
      "Journal de retour de la méthode de sélection de contrôle à distance",
    ykSelectException:
      "Journal d'exception de la méthode de sélection de contrôle à distance",
  },
  baseController: {
    getDeviceInfoStart:
      "Commencer à obtenir les informations du dispositif, ID du dispositif",
    getDeviceInfoSuccess:
      "Obtenir les informations du dispositif réussi, ID du dispositif",
    getDeviceInfoNotFound:
      "Informations du dispositif non trouvées, ID du dispositif",
    getDeviceInfoFailed:
      "Échec de l'obtention des informations du dispositif, ID du dispositif",
  },
  debugInfoMenuController: {
    initialized: "Contrôleur initialisé complètement",
    getDeviceMenuTreeStart:
      "Commencer à obtenir l'arbre de menu du dispositif, ID du dispositif",
    getDeviceMenuTreeCheckConnection:
      "Vérifier l'état de connexion du dispositif, ID du dispositif",
    getDeviceMenuTreeNotConnected:
      "Dispositif non connecté, impossible d'obtenir l'arbre de menu, ID du dispositif",
    getDeviceMenuTreeConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir l'arbre de menu",
    getDeviceMenuTreeSuccess:
      "Obtenir l'arbre de menu du dispositif avec succès, nombre d'éléments de menu",
    getDeviceMenuTreeEmpty: "Arbre de menu vide, ID du dispositif",
    getDeviceMenuTreeException:
      "Exception lors de l'obtention de l'arbre de menu du dispositif, ID du dispositif",
    getTreeItemByNameStart:
      "Commencer à obtenir l'élément de menu, paramètres de demande",
    getTreeItemByNameCheckConnection:
      "Vérifier l'état de connexion du dispositif, ID du dispositif",
    getTreeItemByNameNotConnected:
      "Dispositif non connecté, impossible d'obtenir l'élément de menu, ID du dispositif",
    getTreeItemByNameConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir l'élément de menu",
    getTreeItemByNameSuccess: "Obtenir l'élément de menu avec succès, résultat",
    getTreeItemByNameNotFound: "Élément de menu non trouvé, ID du dispositif",
    getTreeItemByNameException:
      "Exception lors de l'obtention de l'élément de menu, ID du dispositif",
    getGroupInfoListStart:
      "Commencer à obtenir les informations de groupe, paramètres de demande",
    getGroupInfoListCheckConnection:
      "Vérifier l'état de connexion du dispositif, ID du dispositif",
    getGroupInfoListNotConnected:
      "Dispositif non connecté, impossible d'obtenir les informations de groupe, ID du dispositif",
    getGroupInfoListConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les informations de groupe, ID du dispositif",
    getGroupInfoListSuccess:
      "Obtenir les informations de groupe avec succès, résultat",
    getGroupInfoListNotFound:
      "Informations de groupe non trouvées, ID du dispositif",
    getGroupInfoListException:
      "Exception lors de l'obtention des informations de groupe, ID du dispositif",
  },
  deviceFileController: {
    initialized: "Contrôleur initialisé complètement",
    getDeviceFileStart:
      "Commencer à obtenir le répertoire de fichiers du dispositif, paramètres de demande",
    getDeviceFileCheckConnection: "Vérifier l'état de connexion du dispositif",
    getDeviceFileNotConnected:
      "Dispositif non connecté, impossible d'obtenir le répertoire de fichiers",
    getDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir le répertoire de fichiers",
    getDeviceFileSuccess:
      "Obtenir le répertoire de fichiers du dispositif avec succès",
    getDeviceFileException:
      "Exception lors de l'obtention du répertoire de fichiers du dispositif",
    uploadDeviceFileStart:
      "Commencer à télécharger le fichier du dispositif, paramètres de demande",
    uploadDeviceFileCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    uploadDeviceFileNotConnected:
      "Dispositif non connecté, impossible de télécharger le fichier",
    uploadDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour télécharger le fichier",
    uploadDeviceFileSuccess: "Téléchargement de fichier réussi, résultat",
    uploadDeviceFileException: "Exception lors du téléchargement de fichier",
    cancelUploadDeviceFileStart:
      "Commencer à annuler le téléchargement de fichier, paramètres de demande",
    cancelUploadDeviceFileCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    cancelUploadDeviceFileNotConnected:
      "Dispositif non connecté, impossible d'annuler le téléchargement",
    cancelUploadDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour annuler le téléchargement",
    cancelUploadDeviceFileSuccess:
      "Annulation du téléchargement réussie, résultat",
    cancelUploadDeviceFileException:
      "Exception lors de l'annulation du téléchargement",
    downloadDeviceFileStart:
      "Commencer à télécharger le fichier du dispositif, paramètres de demande",
    downloadDeviceFileCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    downloadDeviceFileNotConnected:
      "Dispositif non connecté, impossible de télécharger le fichier",
    downloadDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour télécharger le fichier",
    downloadDeviceFileSuccess: "Téléchargement de fichier réussi, résultat",
    downloadDeviceFileException: "Exception lors du téléchargement de fichier",
    cancelDownloadDeviceFileStart:
      "Commencer à annuler le téléchargement de fichier, paramètres de demande",
    cancelDownloadDeviceFileCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    cancelDownloadDeviceFileNotConnected:
      "Dispositif non connecté, impossible d'annuler le téléchargement",
    cancelDownloadDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour annuler le téléchargement",
    cancelDownloadDeviceFileSuccess:
      "Annulation du téléchargement réussie, résultat",
    cancelDownloadDeviceFileException:
      "Exception lors de l'annulation du téléchargement",
    importDownloadDeviceFileStart:
      "Commencer à importer le fichier téléchargé du dispositif, paramètres de demande",
    importDownloadDeviceFileNotConnected:
      "Dispositif non connecté, impossible d'importer le fichier",
    importDownloadDeviceFileConnected:
      "Dispositif connecté, appeler la couche de service pour importer le fichier",
    importDownloadDeviceFileSuccess: "Importation de fichier réussie, résultat",
    importDownloadDeviceFileException:
      "Exception lors de l'importation de fichier",
    exportDownloadDeviceFileStart:
      "Commencer à exporter le fichier téléchargé du dispositif, paramètres de demande",
    exportDownloadDeviceFileNotConnected:
      "Dispositif non connecté, impossible d'exporter le fichier",
    exportDownloadDeviceFileEmptyPath: "Chemin d'exportation vide",
    exportDownloadDeviceFileCallService:
      "Appeler la couche de service pour exporter le fichier",
    exportDownloadDeviceFileSuccess: "Exportation de fichier réussie, résultat",
    exportDownloadDeviceFileException:
      "Exception lors de l'exportation de fichier",
  },
  deviceOperationController: {
    initialized: "Contrôleur initialisé complètement",
    manualWaveStart:
      "Commencer l'enregistrement manuel d'ondes, paramètres de demande",
    manualWaveCheckConnection: "Vérifier l'état de connexion du dispositif",
    manualWaveNotConnected:
      "Dispositif non connecté, impossible d'exécuter l'enregistrement manuel d'ondes",
    manualWaveConnected:
      "Dispositif connecté, appeler la couche de service pour exécuter l'enregistrement manuel d'ondes",
    manualWaveSuccess:
      "Enregistrement manuel d'ondes exécuté avec succès, résultat",
    manualWaveException:
      "Exception lors de l'exécution de l'enregistrement manuel d'ondes",
    clearWaveStart:
      "Commencer à effacer le rapport d'ondes, paramètres de demande",
    clearWaveCheckConnection: "Vérifier l'état de connexion du dispositif",
    clearWaveNotConnected:
      "Dispositif non connecté, impossible d'effacer le rapport d'ondes",
    clearWaveConnected:
      "Dispositif connecté, appeler la couche de service pour effacer le rapport d'ondes",
    clearWaveSuccess: "Effacement du rapport d'ondes réussi, résultat",
    clearWaveException: "Exception lors de l'effacement du rapport d'ondes",
    resetDeviceStart:
      "Commencer la réinitialisation du dispositif, paramètres de demande",
    resetDeviceCheckConnection: "Vérifier l'état de connexion du dispositif",
    resetDeviceNotConnected:
      "Dispositif non connecté, impossible d'exécuter la réinitialisation du dispositif",
    resetDeviceConnected:
      "Dispositif connecté, appeler la couche de service pour exécuter la réinitialisation du dispositif",
    resetDeviceSuccess:
      "Réinitialisation du dispositif exécutée avec succès, résultat",
    resetDeviceException:
      "Exception lors de l'exécution de la réinitialisation du dispositif",
    clearReportStart: "Commencer à effacer le rapport, paramètres de demande",
    clearReportCheckConnection: "Vérifier l'état de connexion du dispositif",
    clearReportNotConnected:
      "Dispositif non connecté, impossible d'effacer le rapport",
    clearReportConnected:
      "Dispositif connecté, appeler la couche de service pour effacer le rapport",
    clearReportSuccess: "Effacement du rapport réussi, résultat",
    clearReportException: "Exception lors de l'effacement du rapport",
    rebootDeviceStart:
      "Commencer le redémarrage du dispositif, paramètres de demande",
    rebootDeviceCheckConnection: "Vérifier l'état de connexion du dispositif",
    rebootDeviceNotConnected:
      "Dispositif non connecté, impossible d'exécuter le redémarrage du dispositif",
    rebootDeviceConnected:
      "Dispositif connecté, appeler la couche de service pour exécuter le redémarrage du dispositif",
    rebootDeviceSuccess:
      "Redémarrage du dispositif exécuté avec succès, résultat",
    rebootDeviceException:
      "Exception lors de l'exécution du redémarrage du dispositif",
  },
  deviceSummaryController: {
    initialized: "Contrôleur initialisé complètement",
    getSGCountStart:
      "Commencer à obtenir la quantité de SG, paramètres de demande",
    getSGCountCheckConnection:
      "Vérifier l'état de connexion du dispositif, ID du dispositif",
    getSGCountNotConnected:
      "Dispositif non connecté, impossible d'obtenir la quantité de SG, ID du dispositif",
    getSGCountConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir la quantité de SG",
    getSGCountSuccess: "Obtenir la quantité de SG avec succès, résultat",
    getSGCountException:
      "Exception lors de l'obtention de la quantité de SG, ID du dispositif",
    getSummaryInfoStart:
      "Commencer à obtenir les informations de résumé du dispositif, paramètres de demande",
    getSummaryInfoCheckConnection: "Vérifier l'état de connexion du dispositif",
    getSummaryInfoNotConnected:
      "Dispositif non connecté, impossible d'obtenir les informations de résumé du dispositif",
    getSummaryInfoConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les informations de résumé du dispositif",
    getSummaryInfoSuccess:
      "Obtenir les informations de résumé du dispositif avec succès, résultat",
    getSummaryInfoException:
      "Exception lors de l'obtention des informations de résumé du dispositif",
    getParamSummaryStart:
      "Commencer à obtenir les informations de résumé des paramètres, paramètres de demande",
    getParamSummaryCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    getParamSummaryNotConnected:
      "Dispositif non connecté, impossible d'obtenir les informations de résumé des paramètres",
    getParamSummaryConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir les informations de résumé des paramètres",
    getParamSummarySuccess:
      "Obtenir les informations de résumé des paramètres avec succès, résultat",
    getParamSummaryException:
      "Exception lors de l'obtention des informations de résumé des paramètres",
  },
  deviceTimeController: {
    initialized: "Contrôleur initialisé complètement",
    getDeviceTimeStart:
      "Commencer à obtenir l'heure du dispositif, paramètres de demande",
    getDeviceTimeCheckConnection: "Vérifier l'état de connexion du dispositif",
    getDeviceTimeNotConnected:
      "Dispositif non connecté, impossible d'obtenir l'heure du dispositif",
    getDeviceTimeConnected:
      "Dispositif connecté, appeler la couche de service pour obtenir l'heure du dispositif",
    getDeviceTimeSuccess: "Obtenir l'heure du dispositif avec succès, résultat",
    getDeviceTimeException:
      "Exception lors de l'obtention de l'heure du dispositif",
    writeDeviceTimeStart:
      "Commencer à configurer l'heure du dispositif, paramètres de demande",
    writeDeviceTimeCheckConnection:
      "Vérifier l'état de connexion du dispositif",
    writeDeviceTimeNotConnected:
      "Dispositif non connecté, impossible de configurer l'heure du dispositif",
    writeDeviceTimeConnected:
      "Dispositif connecté, appeler la couche de service pour configurer l'heure du dispositif",
    writeDeviceTimeSuccess:
      "Configurer l'heure du dispositif avec succès, résultat",
    writeDeviceTimeException:
      "Exception lors de la configuration de l'heure du dispositif",
  },
  realEventController: {
    initialized: "Contrôleur initialisé complètement",
    subRealEventStart:
      "Commencer à s'abonner aux événements en temps réel, paramètres de demande",
    subRealEventCallService:
      "Appeler la couche de service pour s'abonner aux événements en temps réel",
    subRealEventException:
      "Exception lors de l'abonnement aux événements en temps réel",
    subRealEventServiceResult:
      "Résultat retourné par la couche de service d'abonnement aux événements en temps réel",
    subRealEventFailed:
      "Échec de l'abonnement aux événements en temps réel, informations d'erreur",
    subRealEventServiceError:
      "Erreur du service d'abonnement aux événements, code d'erreur",
    subRealEventSuccess: "Abonnement aux événements en temps réel réussi",
    unSubRealEventStart:
      "Commencer à annuler l'abonnement aux événements en temps réel, paramètres de demande",
    unSubRealEventCallService:
      "Appeler la couche de service pour annuler l'abonnement aux événements en temps réel",
    unSubRealEventException:
      "Exception lors de l'annulation de l'abonnement aux événements en temps réel",
    unSubRealEventServiceResult:
      "Résultat retourné par la couche de service d'annulation d'abonnement aux événements en temps réel",
    unSubRealEventFailed:
      "Échec de l'annulation de l'abonnement aux événements en temps réel, informations d'erreur",
    unSubRealEventServiceError:
      "Erreur du service d'annulation d'abonnement aux événements, code d'erreur",
    unSubRealEventSuccess:
      "Annulation de l'abonnement aux événements en temps réel réussie",
  },
  remoteYxAndYcController: {
    ykycGetGroupDataEntry: "ykycGetGroupData paramètres d'entrée",
    ykycGetGroupDataReturn: "ykycGetGroupData retour",
    ykycGetGroupDataException: "ykycGetGroupData exception",
    exportAllDataEntry: "exportAllData paramètres d'entrée",
    exportAllDataEmptyPath: "Le chemin d'exportation ne peut pas être vide",
    exportAllDataReturn: "exportAllData retour",
    exportAllDataException: "exportAllData exception",
  },
  customInfoService: {
    getAllGroupsEntry: "getAllGroups paramètres d'entrée",
    addMenuEntry: "addMenu paramètres d'entrée",
    groupName: "Nom du groupe",
    editMenuEntry: "editMenu paramètres d'entrée",
    newGroupKeyword: "Nouveau mot-clé du groupe",
    deleteMenuEntry: "deleteMenu paramètres d'entrée",
    addReportEntry: "addReport paramètres d'entrée",
    reportName: "Nom du rapport",
    newName: "Nouveau nom",
    editReportEntry: "editReport paramètres d'entrée",
    newReportName: "Nouveau nom du rapport",
    deleteReportEntry: "deleteReport paramètres d'entrée",
    getLGReportsEntry: "getLGReports paramètres d'entrée",
    getLGReportsSuccess: "getLGReports réussi",
    lgReportsCount: "Nombre de rapports LG",
    getLGReportsError: "getLGReports exception",
  },
  deviceOperationService: {
    rebootDeviceEntry: "rebootDevice paramètres d'entrée",
    rebootDeviceStart: "Début du redémarrage du dispositif",
    rebootDeviceReturn: "rebootDevice retour",
    rebootDeviceError: "rebootDevice exception",
    clearReportEntry: "clearReport paramètres d'entrée",
    clearReportStart: "Début de l'effacement du rapport",
    clearReportReturn: "clearReport retour",
    clearReportError: "clearReport exception",
    resetDeviceEntry: "resetDevice paramètres d'entrée",
    resetDeviceStart: "Début de la réinitialisation du dispositif",
    resetDeviceReturn: "resetDevice retour",
    resetDeviceError: "resetDevice exception",
    clearWaveEntry: "clearWave paramètres d'entrée",
    clearWaveStart: "Début de l'effacement des ondes",
    clearWaveReturn: "clearWave retour",
    clearWaveError: "clearWave exception",
    manualWaveEntry: "manualWave paramètres d'entrée",
    manualWaveStart: "Début de l'enregistrement manuel d'ondes",
    manualWaveReturn: "manualWave retour",
    manualWaveError: "manualWave exception",
  },
  deviceSummaryService: {
    getSGCountEntry: "getSGCount paramètres d'entrée",
    getSGCountReturn: "getSGCount retour",
    getSGCountError: "getSGCount exception",
    getSummaryInfoEntry: "getSummaryInfo paramètres d'entrée",
    getSummaryInfoReturn: "getSummaryInfo retour",
    getSummaryInfoError: "getSummaryInfo exception",
    getParamSummaryEntry: "getParamSummary paramètres d'entrée",
    getParamSummaryReturn: "getParamSummary retour",
    getParamSummaryError: "getParamSummary exception",
  },
  deviceTimeService: {
    getDeviceTimeEntry: "getDeviceTime paramètres d'entrée",
    getDeviceTimeReturn: "getDeviceTime retour",
    getDeviceTimeError: "getDeviceTime exception",
    writeDeviceTimeEntry: "writeDeviceTime paramètres d'entrée",
    writeDeviceTimeReturn: "writeDeviceTime retour",
    writeDeviceTimeError: "writeDeviceTime exception",
  },
  remoteYxAndYcService: {
    ykycGetGroupDataEntry: "ykycGetGroupData paramètres d'entrée",
    ykycGetGroupDataReturn: "ykycGetGroupData retour",
    ykycGetGroupDataError: "ykycGetGroupData exception",
    ykycGetAllGroupDataEntry: "ykycGetAllGroupData paramètres d'entrée",
    exportAllValError: "exportAllVal exception",
  },
};
