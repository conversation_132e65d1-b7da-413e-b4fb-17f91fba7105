import { ProjectDictRequestData, ProjectDictRequestRes, SetProjectDictRequestData, SetProjectDictRes } from "@/api/interface/biz/debug/dictinfo";
import { moduleIpcRequest } from "@/api/request";

const ipc = moduleIpcRequest("controller/debug/dictconfig/");

/**
 * 词条配置API模块
 */
const dictConfigApi = {
  /**
   * 获取工程词条
   * @param requestData 请求参数
   * @returns 词条数据
   */
  getProjectDict(requestData: ProjectDictRequestData) {
    console.log("dictConfigApi.getProjectDict", requestData);
    return ipc.iecInvoke<ProjectDictRequestRes>("getProjectDict", requestData);
  },

  /**
   * 设置工程词条
   * @param requestData 请求参数
   * @returns 设置结果
   */
  setProjectDict(requestData: SetProjectDictRequestData) {
    console.log("dictConfigApi.setProjectDict", requestData);
    return ipc.iecInvoke<SetProjectDictRes>("setProjectDict", requestData);
  }
};

export { dictConfigApi };
