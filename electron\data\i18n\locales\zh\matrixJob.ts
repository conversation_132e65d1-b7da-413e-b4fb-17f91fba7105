/**
 * 矩阵任务作业相关 - 中文
 */
export default {
  // 任务日志
  logs: {
    taskJobParams: "TaskJob 参数",
    pauseTaskJob: "暂停 TaskJob，任务ID",
    resumeTaskJob: "恢复 TaskJob，任务ID，进程ID",
    doTaskStart: "开始执行任务",
    allFinished: "全部任务完成"
  },

  // 任务步骤
  steps: {
    connect: "连接",
    download: "下载", 
    import: "导入"
  },

  // 任务消息
  messages: {
    connectDevice: "连接装置",
    executeFileDownload: "执行文件下载",
    downloadingFile: "下载文件中",
    downloadFileFailed: "下载文件失败",
    fileDownloadCompleted: "文件下载执行完成",
    executeParamImport: "执行定值导入",
    paramValidationFailed: "定值格式校验失败",
    paramImportFailed: "定值导入失败",
    paramImportCompleted: "定值导入执行完成",
    taskCompleted: "任务执行完成",
    deviceConnectionFailed: "装置连接失败",
    deviceRebootSuccess: "重启装置成功"
  },

  // 错误消息
  errors: {
    paramItemModifyError: "定值项修改错误, 错误的条目是，",
    paramConfirmError: "定值确认错误, 错误原因是：",
    paramNotFound: "错误原因是：未找到对应的定值",
    invalidValue: "值不合法",
    description: "描述",
    errorReason: "，错误原因是："
  }
};
