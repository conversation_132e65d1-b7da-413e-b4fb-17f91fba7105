/**
 * 组件切换性能监控工具
 * 用于监控组件切换的性能和状态
 */

interface SwitchMetrics {
  componentKey: string;
  startTime: number;
  endTime?: number;
  fromCache: boolean;
  interrupted: boolean;
  error?: string;
}

class ComponentSwitchMonitor {
  private currentSwitch: SwitchMetrics | null = null;
  private switchHistory: SwitchMetrics[] = [];
  private maxHistorySize = 50; // 最多保存50条历史记录

  /**
   * 开始组件切换监控
   * @param componentKey 组件标识
   * @param fromCache 是否从缓存加载
   */
  startSwitch(componentKey: string, fromCache: boolean = false) {
    const startTime = performance.now();

    // 如果有正在进行的切换，标记为中断
    if (this.currentSwitch && !this.currentSwitch.endTime) {
      this.endSwitch(true);
    }

    this.currentSwitch = {
      componentKey,
      startTime,
      fromCache,
      interrupted: false
    };

    console.log(`🔄 [ComponentSwitch] 开始切换到组件: ${componentKey}${fromCache ? " (从缓存)" : ""}`);
  }

  /**
   * 结束组件切换监控
   * @param interrupted 是否被中断
   */
  endSwitch(interrupted: boolean = false) {
    if (!this.currentSwitch) {
      console.warn("⚠️ [ComponentSwitch] 没有正在进行的组件切换");
      return;
    }

    const endTime = performance.now();
    const duration = endTime - this.currentSwitch.startTime;

    this.currentSwitch.endTime = endTime;
    this.currentSwitch.interrupted = interrupted;

    // 记录到历史
    this.addToHistory({ ...this.currentSwitch });

    // 输出性能日志
    const status = interrupted ? "中断" : "完成";
    const cacheInfo = this.currentSwitch.fromCache ? " (缓存)" : "";
    console.log(`✅ [ComponentSwitch] 组件切换${status}: ${this.currentSwitch.componentKey}${cacheInfo}, 耗时: ${duration.toFixed(2)}ms`);

    // 性能警告
    if (!interrupted && duration > 1000) {
      console.warn(`⚠️ [ComponentSwitch] 组件切换耗时过长: ${duration.toFixed(2)}ms`);
    }

    this.currentSwitch = null;
  }

  /**
   * 记录错误
   * @param error 错误信息
   */
  recordError(error: string) {
    if (this.currentSwitch) {
      this.currentSwitch.error = error;
      console.error(`❌ [ComponentSwitch] 组件切换错误: ${error}`);
      this.endSwitch(true);
    }
  }

  /**
   * 添加到历史记录
   * @param metrics 切换指标
   */
  private addToHistory(metrics: SwitchMetrics) {
    this.switchHistory.unshift(metrics);

    // 限制历史记录大小
    if (this.switchHistory.length > this.maxHistorySize) {
      this.switchHistory = this.switchHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 获取切换历史统计
   */
  getStatistics() {
    const completed = this.switchHistory.filter(s => !s.interrupted && !s.error);
    const interrupted = this.switchHistory.filter(s => s.interrupted);
    const errors = this.switchHistory.filter(s => s.error);
    const cached = this.switchHistory.filter(s => s.fromCache);

    const durations = completed.filter(s => s.endTime).map(s => s.endTime! - s.startTime);

    const avgDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;

    const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;
    const minDuration = durations.length > 0 ? Math.min(...durations) : 0;

    return {
      total: this.switchHistory.length,
      completed: completed.length,
      interrupted: interrupted.length,
      errors: errors.length,
      cached: cached.length,
      avgDuration: Number(avgDuration.toFixed(2)),
      maxDuration: Number(maxDuration.toFixed(2)),
      minDuration: Number(minDuration.toFixed(2))
    };
  }

  /**
   * 打印性能报告
   */
  printReport() {
    const stats = this.getStatistics();

    console.group("📊 [ComponentSwitch] 组件切换性能报告");
    console.log(`总切换次数: ${stats.total}`);
    console.log(`成功完成: ${stats.completed}`);
    console.log(`被中断: ${stats.interrupted}`);
    console.log(`发生错误: ${stats.errors}`);
    console.log(`缓存命中: ${stats.cached}`);

    if (stats.completed > 0) {
      console.log(`平均耗时: ${stats.avgDuration}ms`);
      console.log(`最长耗时: ${stats.maxDuration}ms`);
      console.log(`最短耗时: ${stats.minDuration}ms`);
    }

    console.groupEnd();
  }

  /**
   * 清空历史记录
   */
  clearHistory() {
    this.switchHistory = [];
    console.log("🗑️ [ComponentSwitch] 历史记录已清空");
  }

  /**
   * 获取当前切换状态
   */
  getCurrentSwitch() {
    return this.currentSwitch;
  }

  /**
   * 获取切换历史
   */
  getHistory() {
    return [...this.switchHistory];
  }
}

// 创建全局实例
export const componentSwitchMonitor = new ComponentSwitchMonitor();

// 导出类型
export type { SwitchMetrics };
