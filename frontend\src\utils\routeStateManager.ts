import { Router } from "vue-router";
import { useGlobalStore } from "@/stores/modules/global";
// import { useAuthStore } from "@/stores/modules/auth";

interface RouteState {
  currentPath: string;
  currentQuery: Record<string, any>;
  currentParams: Record<string, any>;
  timestamp: number;
  userState: Record<string, any>;
  appStatus: "running" | "suspended" | "exited";
  sessionId: string;
}

/**
 * 路由状态管理器
 * 负责保存和恢复前端路由状态，防止休眠后状态丢失
 */
class RouteStateManager {
  private static instance: RouteStateManager;
  private router: Router | null = null;
  private saveTimer: number | null = null;
  private readonly STORAGE_KEY = "visualdebug_route_state";
  private readonly MAX_AGE = 24 * 60 * 60 * 1000; // 24小时
  private currentSessionId: string;
  private appStatus: "running" | "suspended" | "exited" = "running";

  private constructor() {
    // 生成当前会话ID
    this.currentSessionId = Date.now().toString() + "-" + Math.random().toString(36).substring(2, 11);
    this.setupEventListeners();
  }

  public static getInstance(): RouteStateManager {
    if (!RouteStateManager.instance) {
      RouteStateManager.instance = new RouteStateManager();
    }
    return RouteStateManager.instance;
  }

  /**
   * 初始化路由状态管理器
   */
  public initialize(router: Router): void {
    this.router = router;
    console.log("[RouteStateManager] 路由状态管理器已初始化");

    // 标记应用为运行状态
    this.setAppStatus("running");

    // 尝试恢复路由状态
    this.restoreRouteState();

    // 开始定时保存
    this.startPeriodicSave();
  }

  /**
   * 设置应用状态
   */
  public setAppStatus(status: "running" | "suspended" | "exited"): void {
    this.appStatus = status;
    console.log(`[RouteStateManager] 应用状态变更为: ${status}`);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听页面卸载事件（应用退出）
    window.addEventListener("beforeunload", () => {
      this.setAppStatus("exited");
      this.clearSavedState(); // 应用退出时清除状态
    });

    // 监听页面隐藏事件（可能是休眠或锁屏）
    document.addEventListener("visibilitychange", () => {
      if (document.hidden) {
        this.setAppStatus("suspended");
        this.saveRouteState();
      } else {
        this.setAppStatus("running");
      }
    });

    // 监听页面焦点丢失事件
    window.addEventListener("blur", () => {
      this.saveRouteState();
    });

    // 监听页面获得焦点事件
    window.addEventListener("focus", () => {
      // 延迟一点时间，确保页面完全激活
      setTimeout(() => {
        this.checkAndRestoreState();
      }, 100);
    });
  }

  /**
   * 保存当前路由状态
   */
  public saveRouteState(): void {
    if (!this.router) {
      console.warn("[RouteStateManager] 路由器未初始化");
      return;
    }

    try {
      const currentRoute = this.router.currentRoute.value;

      // 不保存特殊页面的状态（激活页面、登录页面等）
      const excludePaths = ["/activate", "/login", "/", "/404", "/500"];
      if (excludePaths.includes(currentRoute.path)) {
        console.log("[RouteStateManager] 跳过保存特殊页面状态:", currentRoute.path);
        return;
      }

      const globalStore = useGlobalStore();
      // const authStore = useAuthStore();

      const routeState: RouteState = {
        currentPath: currentRoute.path,
        currentQuery: { ...currentRoute.query },
        currentParams: { ...currentRoute.params },
        timestamp: Date.now(),
        appStatus: this.appStatus,
        sessionId: this.currentSessionId,
        userState: {
          // 保存一些关键的用户状态
          language: globalStore.language,
          layout: globalStore.layout,
          isConsole: globalStore.isConsole,
          consoleHeight: globalStore.consoleHeight,
          isDeviceList: globalStore.isDeviceList,
          isFunctionList: globalStore.isFunctionList
          // 可以添加更多需要保存的状态
        }
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(routeState));
      console.log("[RouteStateManager] 路由状态已保存:", routeState.currentPath);
    } catch (error) {
      console.error("[RouteStateManager] 保存路由状态失败:", error);
    }
  }

  /**
   * 恢复路由状态
   */
  public restoreRouteState(): void {
    if (!this.router) {
      console.warn("[RouteStateManager] 路由器未初始化");
      return;
    }

    try {
      const savedStateStr = localStorage.getItem(this.STORAGE_KEY);
      if (!savedStateStr) {
        console.log("[RouteStateManager] 没有找到保存的路由状态");
        return;
      }

      const savedState: RouteState = JSON.parse(savedStateStr);

      // 检查应用状态，如果是退出状态则不恢复
      if (savedState.appStatus === "exited") {
        console.log("[RouteStateManager] 应用已正常退出，不恢复路由状态");
        localStorage.removeItem(this.STORAGE_KEY);
        return;
      }

      // 检查状态是否过期
      if (Date.now() - savedState.timestamp > this.MAX_AGE) {
        console.log("[RouteStateManager] 路由状态已过期，清除");
        localStorage.removeItem(this.STORAGE_KEY);
        return;
      }

      // 只有在suspended状态下才恢复（表示是休眠/锁屏后恢复）
      if (savedState.appStatus === "suspended") {
        console.log("[RouteStateManager] 检测到休眠状态，准备恢复路由状态:", savedState.currentPath);

        // 恢复用户状态
        this.restoreUserState(savedState.userState);

        // 恢复路由
        if (savedState.currentPath && savedState.currentPath !== "/") {
          // 延迟导航，确保应用完全初始化
          setTimeout(() => {
            this.router
              ?.push({
                path: savedState.currentPath,
                query: savedState.currentQuery
              })
              .catch(error => {
                console.warn("[RouteStateManager] 路由恢复失败:", error);
              });
          }, 1000);
        }
      } else {
        console.log("[RouteStateManager] 应用状态不是suspended，不恢复路由状态");
      }
    } catch (error) {
      console.error("[RouteStateManager] 恢复路由状态失败:", error);
      // 清除损坏的状态
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * 恢复用户状态
   */
  private restoreUserState(userState: Record<string, any>): void {
    try {
      const globalStore = useGlobalStore();

      // 恢复全局状态
      if (userState.language) {
        globalStore.setGlobalState("language", userState.language);
      }
      if (userState.layout) {
        globalStore.setGlobalState("layout", userState.layout);
      }
      if (typeof userState.isConsole === "boolean") {
        globalStore.setGlobalState("isConsole", userState.isConsole);
      }
      if (typeof userState.consoleHeight === "number") {
        globalStore.setGlobalState("consoleHeight", userState.consoleHeight);
      }
      if (typeof userState.isDeviceList === "boolean") {
        globalStore.setGlobalState("isDeviceList", userState.isDeviceList);
      }
      if (typeof userState.isFunctionList === "boolean") {
        globalStore.setGlobalState("isFunctionList", userState.isFunctionList);
      }

      console.log("[RouteStateManager] 用户状态已恢复");
    } catch (error) {
      console.error("[RouteStateManager] 恢复用户状态失败:", error);
    }
  }

  /**
   * 检查并恢复状态（用于页面重新获得焦点时）
   */
  private checkAndRestoreState(): void {
    // 这里可以添加一些逻辑来检查是否需要恢复状态
    // 比如检查当前路由是否是首页，如果是则可能需要恢复
    if (this.router && this.router.currentRoute.value.path === "/") {
      console.log("[RouteStateManager] 检测到可能的状态丢失，尝试恢复");
      this.restoreRouteState();
    }
  }

  /**
   * 开始定时保存
   */
  private startPeriodicSave(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
    }

    // 每2分钟保存一次状态，减少频率
    this.saveTimer = window.setInterval(() => {
      this.saveRouteState();
    }, 120000);

    console.log("[RouteStateManager] 开始定时保存路由状态");
  }

  /**
   * 停止定时保存
   */
  public stopPeriodicSave(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = null;
      console.log("[RouteStateManager] 停止定时保存路由状态");
    }
  }

  /**
   * 清除保存的状态
   */
  public clearSavedState(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    console.log("[RouteStateManager] 已清除保存的路由状态");
  }

  /**
   * 获取保存的状态信息（用于调试）
   */
  public getSavedState(): RouteState | null {
    try {
      const savedStateStr = localStorage.getItem(this.STORAGE_KEY);
      return savedStateStr ? JSON.parse(savedStateStr) : null;
    } catch (error) {
      console.error("[RouteStateManager] 获取保存状态失败:", error);
      return null;
    }
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.stopPeriodicSave();

    // 移除事件监听器
    window.removeEventListener("beforeunload", this.saveRouteState);
    document.removeEventListener("visibilitychange", this.saveRouteState);
    window.removeEventListener("blur", this.saveRouteState);
    window.removeEventListener("focus", this.checkAndRestoreState);

    console.log("[RouteStateManager] 路由状态管理器已销毁");
  }
}

// 导出单例实例
export const routeStateManager = RouteStateManager.getInstance();
export default RouteStateManager;
