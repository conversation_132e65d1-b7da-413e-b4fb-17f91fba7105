/**
 * 错误信息 - 中文
 */
export default {
  success: "成功",
  deviceNotConnected: "装置未连接",
  invalidParam: "参数错误",
  operateFailed: "操作失败",
  noData: "无数据",
  internalError: "内部错误",
  connectionExists: "连接已存在",
  fileContentEmpty: "文件内容为空",
  deviceNotConnectedOrDisconnected: "设备未连接或已断开。",
  getServiceErrorInfo: "未获取对应错误信息",
  saveReportFileError: "保存报告rpt文件出错",
  getConfigureListError: "获取组态列表异常",
  loadConfigureError: "加载组态异常",
  cancelUploadError: "取消录波文件上招异常",
  openWaveFileError: "打开录波文件异常",
  getFileDataSuccess: "获取文件数据内容成功！",
  getHistoryReportError: "获取历史报告错误",
  getSuccessful: "获取成功",
  errorHandledInCatch: "错误已在 catch 里处理",
  waveFileNotFound: "录波文件未找到",
  waveFileSizeZero: "录波文件大小为0",
  uploadException: "上传异常",
  uploadFinished: "上传结束",
  saveReportXlsxError: "保存报告xlsx文件出错",
  invalidXmlStructure: "XML结构无效：缺少configVersion",
  failedToGetTreeMenu: "获取树形菜单失败",
  missingHeaders: "缺失表头",
  excelParseFailed: "Excel解析失败",
  paramModifyError: "定值项修改错误, 错误的条目是，",
  paramConfirmError: "定值确认错误, 错误原因是：",
  errorReason: "，错误原因是：",
  invalidValue: "值不合法",
  errorItem: "错误的条目：",
  description: ", 描述",
  excelFileParseError: "Excel文件解析失败：",
  csvFileParseError: "CSV文件解析失败：",
  xmlFileParseError: "XML文件解析失败：",
  connectionFailed: "连接失败",
  connectionTimeout: "连接超时",
  dataLengthMismatch: "装置返回vkeys数据长度与请求中长度不一致",
  invalidFilePath: "无效的文件路径。请提供有效的.xlsx文件路径。",
  failedToCreateDirectory: "创建目录失败",
  failedToExportData: "导出数据失败",
  worksheetNotFound: "Excel文件不包含指定的工作表。",
  noHeaders: "Excel文件不包含任何表头。",
  parseExcelUnknownError: "解析Excel文件时发生未知错误",
  errorParsingXml: "解析XML时出错",
  failedToUpdateXmlFile: "更新XML文件失败",
  // 控制器错误消息
  addDeviceConfigFailed: "新增设备配置失败",
  updateDeviceConfigFailed: "修改设备配置失败",
  deleteDeviceConfigFailed: "删除设备配置失败",
  deleteSuccess: "删除成功",
  deleteFailed: "删除失败",
  getDeviceList: "获取装置",
  getDeviceConfigListFailed: "获取设备配置列表失败",
  exportFilePathEmpty: "导出文件路径为空",
  exportFileExtensionError: "导出文件后缀填写错误",
  exportFolderCreateFailed: "导出文件夹创建失败",
  variableNameEmpty: "变量名称不能为空",
  exportPathEmpty: "导出路径不能为空",
  importPathEmpty: "导入路径不能为空",
  deviceAlreadyConnected: "装置已连接",
  unknownServiceError: "未知服务错误",
  errorInfo: "错误信息",
  subscribeEventError: "订阅事件错误",
  subscribeEventFailed: "订阅事件失败",
  subscribeEventServiceError: "订阅事件服务错误",
  unsubscribeEventError: "取消订阅事件错误",

  // 新增错误信息
  noDataRetrieved: "未获取数据",
  errorDetail: "错误详情",
  vkeysDataLengthMismatch: "装置返回vkeys数据长度与请求中长度不一致",
  qkeysDataLengthMismatch: "装置返回qkeys数据长度与请求中长度不一致"
};
