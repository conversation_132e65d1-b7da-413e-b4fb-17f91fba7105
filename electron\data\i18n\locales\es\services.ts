/**
 * Relacionado con servicios - Español
 */
export default {
  // Servicio de licencia
  license: {
    cacheResult: "Usando resultado en caché",
    verificationTime: "Tiempo de verificación",
    verificationFailed: "Verificación fallida, tiempo",
    businessErrorInfo: "Obtener información de error de negocio",
    getMachineCode: "Obtener código de máquina",
    checkAuth: "Verificar autorización",
    activate: "Activar licencia",
  },

  // Servicio multiplataforma
  cross: {
    startGoService: "Iniciando servicio Go...",
    goServiceStartSuccess: "Servicio Go iniciado exitosamente, tiempo",
    goServiceStartFailed: "Inicio de servicio Go fallido, tiempo",
    startPythonService: "Iniciando servicio Python...",
    pythonServiceStartSuccess: "Servicio Python iniciado exitosamente, tiempo",
    pythonServiceStartFailed: "Inicio de servicio Python fallido, tiempo",
    optimizeStartParams: "Optimizar parámetros de inicio",
  },

  // Servicio base
  base: {
    getClientStart:
      "Comenzar a obtener cliente de dispositivo, ID de dispositivo",
    deviceNotFound:
      "Información de dispositivo no encontrada, ID de dispositivo",
    deviceNotConnected:
      "Dispositivo no conectado o cliente inválido, ID de dispositivo",
    getClientSuccess:
      "Cliente de dispositivo obtenido exitosamente, ID de dispositivo",
  },

  // Servicio de conexión de dispositivo
  deviceConnect: {
    deviceConnected: "Dispositivo conectado",
    connectionAttempt: "Intento de conexión",
    clientObtained: "Cliente obtenido, ID de dispositivo",
    clientNotObtained: "Cliente no obtenido, ID de dispositivo",
    connectionException: "Excepción de conexión",
    connectionCheckException: "Excepción de verificación de conexión",
    connectDeviceByRpc: "Conectar dispositivo por RPC",
    callInterface: "Llamar interfaz",
    useDeviceIdAsKey: "Usar ID de dispositivo como clave",
    getConnectionFromGlobal: "Obtener objeto de conexión de variables globales",
    notifyFrontendConnectionStatus:
      "Notificar al frontend para modificar estado de conexión",
    retryInterval: "Opcional: intervalo entre reintentos",
    cacheSuccessfulConnection: "Cachear objeto de conexión exitosa",
    resetData: "Restablecer datos",
    compareMd5AndSetFilePath: "Comparar MD5 y establecer ruta de archivo",
    addDebugInfoToGlobal:
      "Agregar debugInfo y debugItemMap a realSingleGlobalDeviceInfo",
    connectionSuccessful: "Conexión exitosa, ID de dispositivo",
    disconnectDevice: "Desconectar dispositivo",
    getConnection: "Obtener conexión",
    deleteConnectionObject: "Eliminar objeto de conexión",
    disconnectSuccessful: "Desconexión exitosa, ID de dispositivo",
    disconnectException: "Excepción de desconexión",
  },

  // Servicio de información de dispositivo
  deviceInfo: {
    getDeviceInfoStart: "Comenzar a obtener información de dispositivo",
    exportStart: "Comenzar exportación de información de dispositivo",
    exportSuccess: "Exportación de información de dispositivo exitosa",
  },

  // Servicio de configuración
  configure: {
    getConfigureList: "Obtener lista de configuración",
    addConfigure: "Agregar configuración",
    setId: "Establecer ID",
    projectNotExists: "El proyecto no existe",
    duplicateName: "Nombre duplicado, por favor reingrese",
    addConfigureException: "Excepción al agregar configuración",
    projectNotFound: "Proyecto no encontrado",
    projectPathNotFound: "Ruta de proyecto no encontrada",
    replaceWithNew: "Reemplazar con nuevo contenido",
    projectNotFoundShort: "Proyecto no encontrado",
    operationTypeIncorrect:
      "Tipo de operación incorrecto, valores permitidos son [project,hmi]",
    renameConfigureException: "Excepción al renombrar configuración",
    getConfigureListException: "Excepción al obtener lista de configuración",
    configureSaveException: "Excepción al guardar configuración",
    openConfigureFolder: "Abrir carpeta de configuración",
  },

  // Servicio de ventana
  window: {
    windowStateInitialized: "Gestión de estado de ventana inicializada",
    windowStateInitFailed:
      "Inicialización de gestión de estado de ventana fallida",
    windowStateSaved: "Estado de ventana guardado",
    saveWindowStateFailed: "Guardar estado de ventana fallido",
    windowStateRestored: "Estado de ventana restaurado",
    restoreWindowStateFailed: "Restaurar estado de ventana fallido",
    clickNotification: "Clic en notificación",
    closeNotification: "Cerrar notificación",
    createWindow: "Crear ventana",
    windowCreated: "Ventana creada",
    windowCreationFailed: "Creación de ventana fallida",
    getWindowId: "Obtener ID de ventana",
    windowCommunication: "Comunicación entre ventanas",
    notificationCreated: "Notificación creada",
    windowClosed: "Ventana cerrada",
    windowMaximized: "Ventana maximizada",
    windowMinimized: "Ventana minimizada",
    windowDragged: "Arrastre de ventana completado",
    screenshotTaken: "Captura de pantalla guardada",
    devToolsOpened: "Herramientas de desarrollador abiertas",
    devToolsClosed: "Herramientas de desarrollador cerradas",
  },

  // Servicio de eventos del sistema
  systemEvents: {
    systemEventsInitialized: "Monitoreo de eventos del sistema inicializado",
    eventListenersSet: "Escuchadores de eventos del sistema configurados",
    setupEventListenersFailed:
      "Configuración de escuchadores de eventos fallida",
    systemSuspending: "Sistema suspendiendo",
    systemResuming: "Sistema reanudando",
    screenLocked: "Pantalla bloqueada",
    screenUnlocked: "Pantalla desbloqueada",
    systemShuttingDown: "Sistema apagándose",
    appBeforeQuit: "Aplicación antes de salir",
    allWindowsClosed: "Todas las ventanas cerradas",
    appActivated: "Aplicación activada",
    windowBlurred: "Ventana sin foco",
    windowFocused: "Ventana con foco",
    windowMinimized: "Ventana minimizada",
    windowRestored: "Ventana restaurada",
    applicationStateSaved: "Estado de aplicación guardado",
    applicationStateRestored: "Estado de aplicación restaurado",
    powerInfoRetrieved: "Información de energía obtenida",
    cleanupCompleted: "Limpieza completada",
  },

  // Servicio de bandeja
  tray: {
    trayLoaded: "Bandeja cargada",
    trayCreated: "Bandeja creada",
    trayCreationFailed: "Creación de bandeja fallida",
  },

  // Servicio de base de datos
  database: {
    databaseConnected: "Base de datos conectada",
    databaseConnectionFailed: "Conexión a base de datos fallida",
    queryExecuted: "Consulta ejecutada",
    queryFailed: "Consulta fallida",
    dataInserted: "Datos insertados",
    dataUpdated: "Datos actualizados",
    dataDeleted: "Datos eliminados",
    transactionStarted: "Transacción iniciada",
    transactionCommitted: "Transacción confirmada",
    transactionRolledBack: "Transacción revertida",
    tableCreated: "Tabla creada",
    tableCreationFailed: "Creación de tabla fallida",
    dataInsertFailed: "Inserción de datos fallida",
    dataDeleteFailed: "Eliminación de datos fallida",
    dataUpdateFailed: "Actualización de datos fallida",
    dataDirRetrieved: "Directorio de datos obtenido",
    dataDirRetrieveFailed: "Obtención de directorio de datos fallida",
    customDataDirSet: "Directorio de datos personalizado establecido",
    customDataDirSetFailed:
      "Establecimiento de directorio de datos personalizado fallido",
  },

  // Servicio HMI
  hmi: {
    graphDefined: "Gráfico definido",
    configurationLoaded: "Configuración cargada",
    viewCreated: "Vista creada",
    projectOpened: "Proyecto abierto",
    projectSaved: "Proyecto guardado",
    elementAdded: "Elemento agregado",
    elementModified: "Elemento modificado",
    elementDeleted: "Elemento eliminado",
  },

  // Servicio de trabajo
  job: {
    jobStarted: "Trabajo iniciado",
    jobCompleted: "Trabajo completado",
    jobFailed: "Trabajo fallido",
    jobCancelled: "Trabajo cancelado",
    jobPaused: "Trabajo pausado",
    jobResumed: "Trabajo reanudado",
    jobScheduled: "Trabajo programado",
    jobRemoved: "Trabajo removido",
  },

  // Servicio de matriz
  matrix: {
    matrixInitialized: "Matriz inicializada",
    matrixCalculationStarted: "Cálculo de matriz iniciado",
    matrixCalculationCompleted: "Cálculo de matriz completado",
    matrixOperationFailed: "Operación de matriz fallida",
    dataProcessed: "Datos procesados",
    algorithmExecuted: "Algoritmo ejecutado",
  },
};
