/**
 * English language pack - Main file
 */
import errors from "./en/errors";
import logs from "./en/logs";
import common from "./en/common";
import dialogs from "./en/dialogs";
import systemFolders from "./en/systemFolders";
import deviceOperations from "./en/deviceOperations";
import configuration from "./en/configuration";
import fileOperations from "./en/fileOperations";
import tray from "./en/tray";
import deviceInfo from "./en/deviceInfo";
import reports from "./en/reports";
import params from "./en/params";
import services from "./en/services";
import backup from "./en/backup";
import deviceFile from "./en/deviceFile";
import variable from "./en/variable";
import matrixJob from "./en/matrixJob";
import hmi from "./en/hmi";
import remoteControl from "./en/remoteControl";
import remoteYxAndYc from "./en/remoteYxAndYc";

export default {
  errors,
  logs,
  common,
  dialogs,
  systemFolders,
  deviceOperations,
  configuration,
  fileOperations,
  tray,
  deviceInfo,
  reports,
  params,
  services,
  backup,
  deviceFile,
  variable,
  matrixJob,
  hmi,
  remoteControl,
  remoteYxAndYc,
};
