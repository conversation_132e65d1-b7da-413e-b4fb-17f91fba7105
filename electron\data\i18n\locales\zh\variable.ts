/**
 * 变量相关 - 中文
 */
export default {
  registerFailed: "注册变量失败，失败原因：",
  modifyFailed: "修改变量失败，失败原因：",
  deleteFailed: "删除变量失败，失败原因：",
  modifyError: "变量修改失败",
  deleteError: "变量删除失败",
  debugVariables: "装置调试变量",
  variableNameExists: "变量名称{name}已经存在！",
  variableNameExistsImport: "变量名称{name}已经存在！",
  importKeyMapping: {
    variableName: "变量名称"
  },
  headers: {
    index: "序号",
    name: "变量名称",
    description: "变量描述",
    type: "变量类型",
    value: "变量值"
  }
};
